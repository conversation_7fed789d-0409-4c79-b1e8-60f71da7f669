import request from "../../../../utils/request";

// 查询报工单分页
export function getReportOrderPageApi(params) {
	return request({
		url: '/scm/mfg/report-order/page',
		method: 'GET',
		params
	})
}

// 查询报工单详情
export function getReportOrderApi(id) {
	return request({
		url: '/scm/mfg/report-order/get?id=' + id,
		method: 'GET'
	})
}

// 根据工单ID查询报工单列表
export function getReportOrderListByWorkOrderIdApi(workOrderId) {
	return request({
		url: '/scm/mfg/report-order/list-by-work-order-id?workOrderId=' + workOrderId,
		method: 'GET'
	})
}

// 新增报工单
export function createReportOrderApi(data) {
	return request({
		url: '/scm/mfg/report-order/create',
		method: 'POST',
		data
	})
}

// 修改报工单
export function updateReportOrderApi(data) {
	return request({
		url: '/scm/mfg/report-order/update',
		method: 'PUT',
		data
	})
}

// 删除报工单
export function deleteReportOrderApi(id) {
	return request({
		url: '/scm/mfg/report-order/delete?id=' + id,
		method: 'DELETE'
	})
}

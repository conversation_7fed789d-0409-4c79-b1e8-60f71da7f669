<template>
	<view class="work-order-item">
		<uni-swipe-action>
			<uni-swipe-action-item :right-options="rightOptions" @click="handleSwipeClick">
				<view class="item-container" @click="handleItemClick">
			<!-- 头部信息：生产单号和状态 -->
			<view class="header-section">
				<view class="work-no-container">
					<text class="work-no">{{ item.workNo || '暂无单号' }}</text>
					<view class="copy-btn" @click.stop="copyWorkNo" v-if="item.workNo">
						<uni-icons type="copy" size="14" color="#666"></uni-icons>
					</view>
				</view>
				<view class="status-badges">
					<view class="status-tag" :style="{ backgroundColor: getStatusColor(item.status, 'work_order_status') }">
						{{ getStatusText(item.status) }}
					</view>
					<view class="approve-tag" :style="{ backgroundColor: getStatusColor(item.approveStatus, 'approve_status') }" v-if="item.approveStatus !== undefined">
						{{ getApproveStatusText(item.approveStatus) }}
					</view>
				</view>
			</view>

			<!-- 产品信息 -->
			<view class="product-section">
				<view class="product-info">
					<text class="product-name">{{ item.productName || '未知产品' }}</text>
					<text class="product-code" v-if="item.productCode">{{ item.productCode }}</text>
				</view>
				<view class="product-spec" v-if="item.spec">
					<text class="spec-text">{{ item.spec }}</text>
				</view>
			</view>

			<!-- 关键信息 -->
			<view class="key-info-section">
				<view class="info-row">
					<view class="info-item">
						<text class="info-label">计划数量</text>
						<text class="info-value">{{ formatQuantity(item.scheduleQuantity) }} {{ getUnitText(item.orderUnit) }}</text>
					</view>
					<view class="info-item" v-if="item.deliverDate">
						<text class="info-label">交期</text>
						<text class="info-value">{{ formatDate(item.deliverDate) }}</text>
					</view>
				</view>
			</view>

			<!-- 进度条 -->
			<view class="progress-section" v-if="item.progress !== undefined && item.progress !== null">
				<view class="progress-header">
					<text class="progress-label">完成进度</text>
					<text class="progress-text">{{ formatProgress(item.progress) }}%</text>
				</view>
				<view class="progress-bar">
					<view class="progress-fill" :style="{ width: formatProgress(item.progress) + '%', backgroundColor: getProgressColor(item.progress) }"></view>
				</view>
			</view>

			<!-- 底部简要信息 -->
			<view class="footer-section" v-if="item.customerName || item.createTime">
				<view class="footer-info">
					<text class="footer-text" v-if="item.customerName">{{ item.customerName }}</text>
					<text class="footer-time" v-if="item.createTime">{{ formatDate(item.createTime) }}</text>
				</view>
			</view>
		</view>
	</view>
			</uni-swipe-action-item>
		</uni-swipe-action>
	</view>
</template>

<script>
import { getDictOptions, getDictLabel, getBatchDictOptions, DICT_TYPE } from '../../../../../../utils/dict';
import { getUnitPageApi } from '../../../../../../api/scm/base/unit';

export default {
	name: 'WorkOrderListItem',
	props: {
		item: {
			type: Object,
			required: true,
			default: () => ({})
		}
	},
	data() {
		return {
			// 字典数据
			dictOptions: {
				work_order_status: [],    // 生产状态
				approve_status: []        // 审批状态
			},
			// 单位数据
			unitList: [],                 // 单位列表
			unitMap: new Map(),           // 单位ID到名称的映射
			// 滑动操作选项
			rightOptions: [
				{
					text: '报工',
					style: {
						backgroundColor: '#007bff',
						color: '#fff'
					}
				}
			]
		}
	},
	computed: {
		// 是否有状态信息需要显示
		hasStatusInfo() {
			return this.item.pickingStatus !== undefined ||
				   this.item.reportStatus !== undefined ||
				   this.item.qualityStatus !== undefined ||
				   this.item.inStockStatus !== undefined;
		}
	},

	async mounted() {
		await this.initDictData();
		await this.loadUnits();
	},

	methods: {
		// 初始化字典数据
		async initDictData() {
			try {
				const dictTypes = [
					DICT_TYPE.WORK_ORDER_STATUS,    // 生产任务单状态
					DICT_TYPE.APPROVE_STATUS        // 审批状态
				];
				const dictMap = await getBatchDictOptions(dictTypes);

				this.dictOptions = {
					work_order_status: dictMap[DICT_TYPE.WORK_ORDER_STATUS] || [],
					approve_status: dictMap[DICT_TYPE.APPROVE_STATUS] || []
				};
			} catch (error) {
				console.error('获取字典数据失败:', error);
				// 保持空数组，getDictLabel会返回原始值
				this.dictOptions = {
					work_order_status: [],
					approve_status: []
				};
			}
		},

		// 加载单位数据
		async loadUnits() {
			try {
				const response = await getUnitPageApi({
					pageNo: 1,
					pageSize: 100
				});

				if (response.code === 0 && response.data && response.data.list) {
					const units = response.data.list;
					this.unitList = units;
					// 建立单位映射
					units.forEach(unit => {
						if (unit && unit.id && unit.name) {
							this.unitMap.set(unit.id, unit.name);
						}
					});
				}
			} catch (error) {
				console.error('加载单位数据失败:', error);
				this.unitList = [];
				this.unitMap = new Map();
			}
		},
		// 处理列表项点击
		handleItemClick() {
			this.$emit('click', this.item);
		},

		// 处理滑动操作点击
		handleSwipeClick(e) {
			console.log('滑动操作点击:', e);
			if (e.index === 0) { // 报工按钮
				this.handleReportWork();
			}
		},

		// 处理报工操作
		handleReportWork() {
			// 检查工单状态，只有审核通过的工单才能报工
			if (this.item.approveStatus !== 3) {
				uni.showToast({
					title: '工单未审核通过，无法报工',
					icon: 'none'
				});
				return;
			}

			// 跳转到报工页面
			uni.navigateTo({
				url: `/pages/biz/scm/mfg/workorder/reportWork?workOrderId=${this.item.id}&workNo=${this.item.workNo}`
			});
		},

		// 复制生产单号
		copyWorkNo() {
			if (this.item.workNo) {
				// #ifdef H5
				if (navigator.clipboard) {
					navigator.clipboard.writeText(this.item.workNo).then(() => {
						uni.showToast({
							title: '复制成功',
							icon: 'success'
						});
					});
				} else {
					// 降级方案
					const textArea = document.createElement('textarea');
					textArea.value = this.item.workNo;
					document.body.appendChild(textArea);
					textArea.select();
					document.execCommand('copy');
					document.body.removeChild(textArea);
					uni.showToast({
						title: '复制成功',
						icon: 'success'
					});
				}
				// #endif

				// #ifdef MP-WEIXIN
				uni.setClipboardData({
					data: this.item.workNo,
					success: () => {
						uni.showToast({
							title: '复制成功',
							icon: 'success'
						});
					}
				});
				// #endif

				// #ifdef APP-PLUS
				uni.setClipboardData({
					data: this.item.workNo,
					success: () => {
						uni.showToast({
							title: '复制成功',
							icon: 'success'
						});
					}
				});
				// #endif
			}
		},

		// 获取生产状态文本
		getStatusText(status) {
			return getDictLabel(this.dictOptions.work_order_status, status) || '未知状态';
		},

		// 获取审批状态文本
		getApproveStatusText(status) {
			return getDictLabel(this.dictOptions.approve_status, status) || '未知';
		},



		// 格式化数量
		formatQuantity(value) {
			if (value === null || value === undefined || value === '') return '0';
			if (isNaN(value)) return '0';
			return Number(value).toLocaleString();
		},

		// 获取单位文本
		getUnitText(unit) {
			if (!unit) return '';
			// 使用单位映射获取单位名称
			const unitId = typeof unit === 'string' ? parseInt(unit) : unit;
			const unitName = this.unitMap.get(unitId);
			return unitName || unit.toString();
		},

		// 格式化日期（简化版）
		formatDate(timestamp) {
			if (!timestamp) return '-';
			const date = new Date(timestamp);
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${month}-${day}`;
		},

		// 格式化日期时间
		formatDateTime(timestamp) {
			if (!timestamp) return '-';
			const date = new Date(timestamp);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			return `${year}-${month}-${day} ${hours}:${minutes}`;
		},

		// 格式化进度
		formatProgress(progress) {
			if (progress === null || progress === undefined) return 0;
			const num = parseFloat(progress);
			return isNaN(num) ? 0 : Math.min(100, Math.max(0, num));
		},

		// 获取进度条颜色
		getProgressColor(progress) {
			const num = this.formatProgress(progress);
			if (num >= 100) return '#28a745'; // 绿色 - 已完成
			if (num >= 50) return '#007bff';  // 蓝色 - 进行中
			if (num > 0) return '#ffc107';    // 黄色 - 刚开始
			return '#6c757d';                 // 灰色 - 未开始
		},

		// 获取状态标签的颜色
		getStatusColor(status, dictType) {
			const dictOption = this.dictOptions[dictType]?.find(item => String(item.value) === String(status));
			if (dictOption && dictOption.colorType) {
				// 根据字典中的colorType返回对应颜色
				const colorMap = {
					'primary': '#007bff',
					'success': '#28a745',
					'warning': '#ffc107',
					'danger': '#dc3545',
					'info': '#6c757d'
				};
				return colorMap[dictOption.colorType] || '#6c757d';
			}

			// 简化的默认颜色映射
			const statusNum = parseInt(status) || 0;
			if (statusNum === 0) return '#6c757d'; // 灰色 - 未开始/待处理
			if (statusNum === 1) return '#ffc107'; // 黄色 - 进行中
			if (statusNum === 2) return '#007bff'; // 蓝色 - 处理中
			if (statusNum >= 3) return '#28a745';  // 绿色 - 完成
			return '#6c757d'; // 默认灰色
		}
	}
}
</script>

<style lang="scss" scoped>
.work-order-item {
	margin-bottom: 16rpx;
	background-color: white;
	border-radius: 12rpx;
	border: 1px solid #e5e5e5;
	overflow: hidden;

	&:active {
		background-color: #f8f9fa;
	}
}

.item-container {
	padding: 20rpx;
}

/* 头部区域 */
.header-section {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 16rpx;
}

.work-no-container {
	display: flex;
	align-items: center;
	flex: 1;

	.work-no {
		font-size: 30rpx;
		font-weight: 600;
		color: #333;
		margin-right: 12rpx;
	}

	.copy-btn {
		padding: 6rpx;
		border-radius: 6rpx;
		background-color: #f5f5f5;

		&:active {
			background-color: #e5e5e5;
		}
	}
}

.status-badges {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 6rpx;
}

.status-tag, .approve-tag {
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
	font-size: 22rpx;
	font-weight: 500;
	color: white;
	text-align: center;
	min-width: 80rpx;
}

/* 产品信息区域 */
.product-section {
	margin-bottom: 16rpx;
	padding: 16rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
}

.product-info {
	display: flex;
	align-items: center;
	margin-bottom: 8rpx;

	.product-name {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
		margin-right: 12rpx;
		flex: 1;
		line-height: 1.4;
	}

	.product-code {
		font-size: 22rpx;
		color: #666;
		background-color: #e9ecef;
		padding: 4rpx 8rpx;
		border-radius: 6rpx;
		font-weight: 500;
	}
}

.product-spec {
	.spec-text {
		font-size: 24rpx;
		color: #666;
	}
}

/* 关键信息区域 */
.key-info-section {
	margin-bottom: 16rpx;
}

.info-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.info-item {
	flex: 1;
	margin-right: 20rpx;

	&:last-child {
		margin-right: 0;
	}

	.info-label {
		font-size: 22rpx;
		color: #666;
		display: block;
		margin-bottom: 4rpx;
	}

	.info-value {
		font-size: 24rpx;
		color: #333;
		font-weight: 500;
	}
}

/* 进度区域 */
.progress-section {
	margin-bottom: 16rpx;
	padding: 16rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
}

.progress-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;

	.progress-label {
		font-size: 24rpx;
		color: #666;
		font-weight: 500;
	}

	.progress-text {
		font-size: 26rpx;
		color: #333;
		font-weight: 600;
	}
}

.progress-bar {
	height: 12rpx;
	background-color: #e9ecef;
	border-radius: 6rpx;
	overflow: hidden;

	.progress-fill {
		height: 100%;
		border-radius: 6rpx;
		transition: width 0.3s ease;
	}
}



/* 底部简要信息区域 */
.footer-section {
	border-top: 1px solid #e5e5e5;
	padding-top: 12rpx;
	margin-top: 12rpx;
}

.footer-info {
	display: flex;
	justify-content: space-between;
	align-items: center;

	.footer-text {
		font-size: 22rpx;
		color: #666;
		flex: 1;
	}

	.footer-time {
		font-size: 20rpx;
		color: #999;
	}
}
</style>
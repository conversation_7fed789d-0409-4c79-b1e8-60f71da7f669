<template>
	<view class="report-work-page">
		<!-- 导航栏 -->
		<uni-nav-bar 
			:title="'报工 - ' + (workOrderInfo.workNo || '')" 
			left-icon="back" 
			@clickLeft="goBack"
			background-color="#007bff"
			color="#fff"
		/>

		<!-- 工单信息卡片 -->
		<view class="work-order-card">
			<view class="card-header">
				<text class="card-title">工单信息</text>
			</view>
			<view class="card-content">
				<view class="info-row">
					<text class="label">生产编号：</text>
					<text class="value">{{ workOrderInfo.workNo || '-' }}</text>
				</view>
				<view class="info-row">
					<text class="label">产品名称：</text>
					<text class="value">{{ workOrderInfo.productName || '-' }}</text>
				</view>
				<view class="info-row">
					<text class="label">计划数量：</text>
					<text class="value">{{ formatQuantity(workOrderInfo.scheduleQuantity) }} {{ getUnitText(workOrderInfo.orderUnit) }}</text>
				</view>
			</view>
		</view>

		<!-- 报工表单 -->
		<view class="form-container">
			<uni-forms ref="formRef" :model="formData" :rules="formRules" label-width="120">
				<!-- 任务类型 -->
				<uni-forms-item label="任务类型" name="type" required>
					<uni-data-select 
						v-model="formData.type" 
						:localdata="taskTypeOptions"
						placeholder="请选择任务类型"
					/>
				</uni-forms-item>

				<!-- 开始时间 -->
				<uni-forms-item label="开始时间" name="startTime" required>
					<uni-datetime-picker 
						v-model="formData.startTime" 
						type="datetime"
						placeholder="请选择开始时间"
						@change="calculateCostTime"
					/>
				</uni-forms-item>

				<!-- 结束时间 -->
				<uni-forms-item label="结束时间" name="endTime" required>
					<uni-datetime-picker 
						v-model="formData.endTime" 
						type="datetime"
						placeholder="请选择结束时间"
						@change="calculateCostTime"
					/>
				</uni-forms-item>

				<!-- 用时显示 -->
				<uni-forms-item label="用时">
					<view class="duration-display">
						<text>{{ formatDuration(formData.costTime) }}</text>
					</view>
				</uni-forms-item>

				<!-- 人数 -->
				<uni-forms-item label="人数" name="costHeadcount" required>
					<uni-easyinput 
						v-model="formData.costHeadcount" 
						type="number"
						placeholder="请输入人数"
					/>
				</uni-forms-item>

				<!-- 生产线 -->
				<uni-forms-item label="生产线" name="line">
					<uni-data-select 
						v-model="formData.line" 
						:localdata="productionLineOptions"
						placeholder="请选择生产线"
					/>
				</uni-forms-item>

				<!-- 数量 -->
				<uni-forms-item label="数量" name="quantity" required>
					<uni-easyinput 
						v-model="formData.quantity" 
						type="number"
						placeholder="请输入数量"
					/>
				</uni-forms-item>

				<!-- 件数 -->
				<uni-forms-item label="件数" name="piece">
					<uni-easyinput 
						v-model="formData.piece" 
						type="number"
						placeholder="请输入件数"
					/>
				</uni-forms-item>

				<!-- 批号 -->
				<uni-forms-item label="批号" name="batchNo">
					<uni-easyinput 
						v-model="formData.batchNo" 
						placeholder="请输入批号"
					/>
				</uni-forms-item>

				<!-- 温度 -->
				<uni-forms-item label="温度" name="temperature">
					<uni-easyinput 
						v-model="formData.temperature" 
						type="number"
						placeholder="请输入温度"
					/>
				</uni-forms-item>

				<!-- 湿度 -->
				<uni-forms-item label="湿度" name="humidity">
					<uni-easyinput 
						v-model="formData.humidity" 
						type="number"
						placeholder="请输入湿度"
					/>
				</uni-forms-item>

				<!-- 备注 -->
				<uni-forms-item label="备注" name="remark">
					<uni-easyinput 
						v-model="formData.remark" 
						type="textarea"
						placeholder="请输入备注"
						:maxlength="200"
					/>
				</uni-forms-item>
			</uni-forms>
		</view>

		<!-- 底部操作按钮 -->
		<view class="bottom-actions">
			<button class="cancel-btn" @click="goBack">取消</button>
			<button class="submit-btn" @click="submitForm" :loading="submitting">提交报工</button>
		</view>
	</view>
</template>

<script>
import { getDictOptions, DICT_TYPE } from '../../../../../utils/dict';
import { getWorkOrderApi } from '../../../../../api/scm/mfg/workorder';
import { createReportOrderApi } from '../../../../../api/scm/mfg/reportorder';
import { getUnitPageApi } from '../../../../../api/scm/base/unit';

export default {
	name: 'ReportWork',
	data() {
		return {
			workOrderId: null,
			workOrderInfo: {},
			submitting: false,
			// 表单数据
			formData: {
				workId: null,
				workNo: '',
				type: '2', // 默认生产
				reportCode: '',
				startTime: '',
				endTime: '',
				costTime: 0,
				costHeadcount: 1,
				line: '',
				quantity: '',
				piece: '',
				batchNo: '',
				temperature: '',
				humidity: '',
				remark: ''
			},
			// 表单验证规则
			formRules: {
				type: {
					rules: [{ required: true, errorMessage: '请选择任务类型' }]
				},
				startTime: {
					rules: [{ required: true, errorMessage: '请选择开始时间' }]
				},
				endTime: {
					rules: [{ required: true, errorMessage: '请选择结束时间' }]
				},
				costHeadcount: {
					rules: [{ required: true, errorMessage: '请输入人数' }]
				},
				quantity: {
					rules: [{ required: true, errorMessage: '请输入数量' }]
				}
			},
			// 选项数据
			taskTypeOptions: [],
			productionLineOptions: [],
			unitMap: new Map()
		}
	},

	async onLoad(options) {
		if (options.workOrderId) {
			this.workOrderId = options.workOrderId;
			await this.loadWorkOrderInfo();
		}
		await this.initDictData();
		await this.loadUnits();
		this.initFormData();
	},

	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},

		// 加载工单信息
		async loadWorkOrderInfo() {
			try {
				const response = await getWorkOrderApi(this.workOrderId);
				if (response.code === 0) {
					this.workOrderInfo = response.data;
				}
			} catch (error) {
				console.error('加载工单信息失败:', error);
				uni.showToast({
					title: '加载工单信息失败',
					icon: 'none'
				});
			}
		},

		// 初始化字典数据
		async initDictData() {
			try {
				// 任务类型
				const taskTypes = await getDictOptions(DICT_TYPE.MFG_WORK_TYPE);
				this.taskTypeOptions = taskTypes.map(item => ({
					value: item.value,
					text: item.label
				}));

				// 生产线
				const productionLines = await getDictOptions(DICT_TYPE.MANUFACTURE_LINE);
				this.productionLineOptions = productionLines.map(item => ({
					value: item.value,
					text: item.label
				}));
			} catch (error) {
				console.error('加载字典数据失败:', error);
			}
		},

		// 加载单位数据
		async loadUnits() {
			try {
				const response = await getUnitPageApi({
					pageNo: 1,
					pageSize: 100
				});

				if (response.code === 0 && response.data && response.data.list) {
					const units = response.data.list;
					units.forEach(unit => {
						if (unit && unit.id && unit.name) {
							this.unitMap.set(unit.id, unit.name);
						}
					});
				}
			} catch (error) {
				console.error('加载单位数据失败:', error);
			}
		},

		// 初始化表单数据
		initFormData() {
			if (this.workOrderInfo) {
				this.formData.workId = this.workOrderInfo.id;
				this.formData.workNo = this.workOrderInfo.workNo;
				this.formData.line = this.workOrderInfo.scheduleLine || this.workOrderInfo.actualLine;
				this.formData.quantity = this.workOrderInfo.scheduleQuantity;
				this.formData.piece = this.workOrderInfo.schedulePiece;
				this.formData.costHeadcount = this.workOrderInfo.scheduleHeadcount || 1;
				
				// 生成批号（当前年月日）
				const now = new Date();
				const year = now.getFullYear();
				const month = String(now.getMonth() + 1).padStart(2, '0');
				const day = String(now.getDate()).padStart(2, '0');
				this.formData.batchNo = `${year}${month}${day}`;

				// 设置默认时间
				if (this.workOrderInfo.scheduleStartTime) {
					this.formData.startTime = this.workOrderInfo.scheduleStartTime;
				}
				if (this.workOrderInfo.scheduleEndTime) {
					this.formData.endTime = this.workOrderInfo.scheduleEndTime;
				}

				this.calculateCostTime();
			}
		},

		// 计算用时
		calculateCostTime() {
			if (this.formData.startTime && this.formData.endTime) {
				const startTime = new Date(this.formData.startTime).getTime();
				const endTime = new Date(this.formData.endTime).getTime();
				if (endTime > startTime) {
					this.formData.costTime = Math.round((endTime - startTime) / (1000 * 60)); // 转换为分钟
				} else {
					this.formData.costTime = 0;
				}
			}
		},

		// 格式化时长显示
		formatDuration(minutes) {
			if (!minutes || minutes <= 0) return '0分钟';
			const hours = Math.floor(minutes / 60);
			const mins = minutes % 60;
			if (hours > 0) {
				return mins > 0 ? `${hours}小时${mins}分钟` : `${hours}小时`;
			}
			return `${mins}分钟`;
		},

		// 格式化数量
		formatQuantity(value) {
			if (value === null || value === undefined || value === '') return '0';
			if (isNaN(value)) return '0';
			return Number(value).toLocaleString();
		},

		// 获取单位文本
		getUnitText(unit) {
			if (!unit) return '';
			const unitId = typeof unit === 'string' ? parseInt(unit) : unit;
			const unitName = this.unitMap.get(unitId);
			return unitName || unit.toString();
		},

		// 提交表单
		async submitForm() {
			try {
				// 表单验证
				const valid = await this.$refs.formRef.validate();
				if (!valid) {
					return;
				}

				// 检查时间逻辑
				if (this.formData.startTime && this.formData.endTime) {
					const startTime = new Date(this.formData.startTime).getTime();
					const endTime = new Date(this.formData.endTime).getTime();
					if (endTime <= startTime) {
						uni.showToast({
							title: '结束时间必须大于开始时间',
							icon: 'none'
						});
						return;
					}
				}

				this.submitting = true;

				// 准备提交数据
				const submitData = {
					...this.formData,
					startTime: this.formData.startTime ? new Date(this.formData.startTime).getTime() : null,
					endTime: this.formData.endTime ? new Date(this.formData.endTime).getTime() : null,
					costTime: this.formData.costTime.toString(), // 转换为字符串
					costHeadcount: parseInt(this.formData.costHeadcount) || 1,
					quantity: parseFloat(this.formData.quantity) || 0,
					piece: parseInt(this.formData.piece) || 0,
					temperature: parseFloat(this.formData.temperature) || null,
					humidity: parseFloat(this.formData.humidity) || null
				};

				console.log('提交报工数据:', submitData);

				const response = await createReportOrderApi(submitData);

				if (response.code === 0) {
					uni.showToast({
						title: '报工成功',
						icon: 'success'
					});

					// 延迟返回上一页
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				} else {
					uni.showToast({
						title: response.msg || '报工失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('提交报工失败:', error);
				uni.showToast({
					title: '提交失败，请重试',
					icon: 'none'
				});
			} finally {
				this.submitting = false;
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.report-work-page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.work-order-card {
	margin: 20rpx;
	background-color: white;
	border-radius: 12rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

	.card-header {
		padding: 24rpx;
		background-color: #007bff;
		
		.card-title {
			color: white;
			font-size: 32rpx;
			font-weight: 600;
		}
	}

	.card-content {
		padding: 24rpx;

		.info-row {
			display: flex;
			margin-bottom: 16rpx;
			
			&:last-child {
				margin-bottom: 0;
			}

			.label {
				font-size: 28rpx;
				color: #666;
				width: 160rpx;
			}

			.value {
				font-size: 28rpx;
				color: #333;
				flex: 1;
			}
		}
	}
}

.form-container {
	margin: 20rpx;
	background-color: white;
	border-radius: 12rpx;
	padding: 24rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.duration-display {
	padding: 16rpx 0;
	
	text {
		font-size: 28rpx;
		color: #333;
	}
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	padding: 24rpx;
	background-color: white;
	border-top: 1px solid #e5e5e5;
	gap: 24rpx;

	.cancel-btn, .submit-btn {
		flex: 1;
		height: 88rpx;
		border-radius: 8rpx;
		font-size: 32rpx;
		border: none;
	}

	.cancel-btn {
		background-color: #f8f9fa;
		color: #666;
	}

	.submit-btn {
		background-color: #007bff;
		color: white;
	}
}
</style>

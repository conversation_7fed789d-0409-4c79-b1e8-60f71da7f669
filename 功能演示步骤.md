# 工单报工功能演示步骤

## 功能演示流程

### 1. 工单列表滑动操作演示

**步骤：**
1. 打开工单列表页面 (`pages/biz/scm/mfg/workorder/workOrder.vue`)
2. 找到一个审核通过的工单项（状态显示为审核通过）
3. 在工单项上向左滑动
4. 观察右侧出现蓝色的"报工"按钮
5. 点击"报工"按钮

**预期结果：**
- 滑动操作流畅，显示报工按钮
- 点击后跳转到报工页面，并自动填充工单信息

### 2. 工单详情页报工按钮演示

**步骤：**
1. 从工单列表点击任意工单项进入详情页
2. 在详情页面查看"操作"卡片区域
3. 如果工单已审核通过，会显示蓝色的"报工"按钮
4. 点击"报工"按钮

**预期结果：**
- 只有审核通过的工单才显示报工按钮
- 点击后跳转到报工页面

### 3. 报工表单功能演示

**步骤：**
1. 通过上述任一方式进入报工页面
2. 观察页面自动填充的工单信息
3. 填写必填字段：
   - 任务类型：选择"生产"
   - 开始时间：选择当前时间
   - 结束时间：选择稍后的时间
   - 人数：输入数字（如：2）
   - 数量：输入数字（如：100）
4. 观察用时自动计算
5. 填写可选字段（生产线、件数、批号等）
6. 点击"提交报工"按钮

**预期结果：**
- 工单信息正确显示
- 时间选择后自动计算用时
- 表单验证正常工作
- 提交成功后显示成功提示并返回上一页

## 测试要点

### 1. 权限控制测试
- **测试场景**：选择未审核通过的工单
- **预期结果**：滑动时不显示报工按钮，或点击时提示"工单未审核通过，无法报工"

### 2. 表单验证测试
- **必填字段验证**：不填写必填字段时应显示错误提示
- **时间逻辑验证**：结束时间早于开始时间时应显示错误提示
- **数字格式验证**：人数、数量等字段应只接受数字输入

### 3. 数据自动填充测试
- **工单信息填充**：工单号、产品名称、计划数量等应正确显示
- **默认值设置**：批号应自动设置为当前日期（YYYYMMDD格式）
- **时间计算**：修改开始或结束时间后，用时应自动重新计算

### 4. 网络请求测试
- **工单信息加载**：页面加载时应正确获取工单详情
- **字典数据加载**：任务类型和生产线选项应正确加载
- **报工提交**：提交时应正确调用API并处理响应

## 可能遇到的问题及解决方案

### 1. 滑动操作不生效
**可能原因：**
- uni-swipe-action组件未正确导入
- 组件版本不兼容

**解决方案：**
- 检查uni_modules目录中是否存在uni-swipe-action
- 确认组件版本与uni-app版本兼容

### 2. 字典数据加载失败
**可能原因：**
- 字典类型定义不正确
- API接口返回数据格式不匹配

**解决方案：**
- 检查utils/dict.js中的字典类型定义
- 确认后端字典数据接口正常

### 3. 报工提交失败
**可能原因：**
- API接口地址不正确
- 数据格式不符合后端要求

**解决方案：**
- 检查api/scm/mfg/reportorder/index.js中的接口定义
- 确认提交的数据格式与后端API文档一致

### 4. 页面跳转失败
**可能原因：**
- pages.json中未正确配置路由
- 页面路径不正确

**解决方案：**
- 检查pages.json中是否添加了reportWork页面配置
- 确认页面路径拼写正确

## 调试建议

### 1. 开启控制台日志
在关键位置添加console.log输出，观察：
- 滑动事件是否正确触发
- 工单数据是否正确加载
- 表单数据是否正确提交

### 2. 网络请求监控
使用开发者工具监控网络请求：
- 工单详情API调用
- 字典数据API调用
- 报工提交API调用

### 3. 错误处理
确保所有异步操作都有适当的错误处理：
- try-catch包装异步调用
- 用户友好的错误提示
- 网络异常的降级处理

## 性能优化建议

1. **数据缓存**：字典数据可以考虑缓存，避免重复请求
2. **懒加载**：非必要的数据可以延迟加载
3. **防抖处理**：表单提交按钮添加防抖，避免重复提交
4. **内存管理**：页面销毁时清理定时器和事件监听器

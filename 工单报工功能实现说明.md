# 工单报工功能实现说明

## 功能概述

根据用户需求，为uniapp项目中的工单管理系统添加了滑动操作和报工功能，具体包括：

1. **工单列表项滑动操作**：在工单列表中，每个工单项支持向左滑动显示"报工"按钮
2. **工单详情页报工按钮**：在工单详情页面添加了报工操作按钮
3. **独立报工页面**：创建了专门的报工表单页面，支持完整的报工信息录入

## 实现的文件修改

### 1. 工单列表项组件 (`pages/biz/scm/mfg/workorder/components/workOrderListItem.vue`)

**主要修改：**
- 使用 `uni-swipe-action` 组件包装原有的列表项内容
- 添加右侧滑动操作选项，包含"报工"按钮
- 添加滑动操作处理方法 `handleSwipeClick`
- 添加报工操作方法 `handleReportWork`，包含状态检查逻辑

**关键代码：**
```vue
<uni-swipe-action>
  <uni-swipe-action-item :right-options="rightOptions" @click="handleSwipeClick">
    <!-- 原有的工单项内容 -->
  </uni-swipe-action-item>
</uni-swipe-action>
```

**业务逻辑：**
- 只有审核通过（approveStatus === 3）的工单才能进行报工操作
- 点击报工按钮后跳转到报工页面，传递工单ID和工单号参数

### 2. 工单详情页 (`pages/biz/scm/mfg/workorder/detail.vue`)

**主要修改：**
- 在基本信息卡片后添加操作按钮区域
- 添加报工按钮，仅在工单审核通过时显示
- 添加报工操作处理方法 `handleReportWork`
- 添加操作按钮相关的CSS样式

**关键功能：**
- 状态检查：只有审核通过的工单才显示报工按钮
- 页面跳转：点击报工按钮跳转到报工页面

### 3. 报工页面 (`pages/biz/scm/mfg/workorder/reportWork.vue`)

**新建页面，主要功能：**
- 显示工单基本信息（工单号、产品名称、计划数量等）
- 提供完整的报工表单，包括：
  - 任务类型（字典选择）
  - 开始时间和结束时间（日期时间选择器）
  - 用时自动计算显示
  - 人数、生产线、数量、件数等基本信息
  - 批号、温度、湿度等生产环境信息
  - 备注信息
- 表单验证和数据提交
- 自动填充工单相关信息

**数据处理：**
- 根据传入的工单ID自动加载工单信息
- 自动填充默认值（如批号使用当前日期）
- 时间计算：自动计算开始时间和结束时间的差值
- 数据格式转换：确保提交的数据格式符合API要求

### 4. API接口扩展 (`api/scm/mfg/reportorder/index.js`)

**添加的API方法：**
- `createReportOrderApi(data)` - 创建报工单
- `updateReportOrderApi(data)` - 修改报工单  
- `deleteReportOrderApi(id)` - 删除报工单

### 5. 路由配置 (`pages.json`)

**添加报工页面路由：**
```json
{
  "path": "pages/biz/scm/mfg/workorder/reportWork",
  "style": {
    "navigationBarTitleText": "工单报工",
    "enablePullDownRefresh": false
  }
}
```

## 技术特点

### 1. 组件化设计
- 使用uni-app官方组件库中的 `uni-swipe-action` 实现滑动操作
- 使用 `uni-forms`、`uni-datetime-picker` 等组件构建表单
- 保持与现有项目风格一致的UI设计

### 2. 数据验证
- 前端表单验证：必填字段检查、时间逻辑验证
- 业务逻辑验证：工单状态检查、权限控制

### 3. 用户体验优化
- 滑动操作提供直观的交互方式
- 自动数据填充减少用户输入工作量
- 实时时间计算和格式化显示
- 加载状态和错误提示

### 4. 响应式设计
- 适配不同屏幕尺寸
- 移动端友好的触摸操作
- 合理的间距和字体大小

## 使用说明

### 工单列表操作
1. 在工单列表中，向左滑动任意工单项
2. 显示"报工"按钮（蓝色背景）
3. 点击"报工"按钮跳转到报工页面

### 工单详情操作  
1. 进入工单详情页面
2. 如果工单已审核通过，会显示"操作"卡片
3. 点击"报工"按钮跳转到报工页面

### 报工表单填写
1. 系统自动填充工单相关信息
2. 选择任务类型（必填）
3. 设置开始和结束时间（必填）
4. 填写人数和数量（必填）
5. 根据需要填写其他信息
6. 点击"提交报工"完成操作

## 注意事项

1. **权限控制**：只有审核通过（approveStatus === 3）的工单才能进行报工操作
2. **数据完整性**：报工时会自动关联工单信息，确保数据一致性
3. **时间逻辑**：结束时间必须大于开始时间
4. **网络处理**：包含完整的错误处理和用户提示机制

## 后续扩展建议

1. 可以考虑添加报工记录查看功能
2. 支持报工单的编辑和删除操作
3. 添加报工数据的统计和分析功能
4. 支持批量报工操作
